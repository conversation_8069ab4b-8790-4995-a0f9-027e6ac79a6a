[{"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\Users\\p_a_v_i_l_i_o_n\\Downloads\\TMS-App-main\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\RNDateTimePickerCGen-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\13e5db451150cc1628879224ca4ec527\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\6d39f3c47cd1da8be1490cfe653aeb09\\RNCSafeAreaViewShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7a82f62356152691476f484d154cdb43\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7c9a9b9ddd584b804e386d6af7996bf8\\safeareacontext\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7c9a9b9ddd584b804e386d6af7996bf8\\safeareacontext\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\e14c72371b9e72604b62e770a10ddb9f\\components\\safeareacontext\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\e14c72371b9e72604b62e770a10ddb9f\\components\\safeareacontext\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\e14c72371b9e72604b62e770a10ddb9f\\components\\safeareacontext\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7ae6edf3a95bddbbd82becc708e383d5\\safeareacontextJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\c7dab0d178ee3bd66f90dc036bf5f8fe\\codegen\\jni\\safeareacontext-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c7b0a5250dae0038c1bb05670df25edb\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c7b0a5250dae0038c1bb05670df25edb\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\145dc39712be167fc121da32c01dd086\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5e02f7cebdf86fa057f0d293b084e9c4\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5e02f7cebdf86fa057f0d293b084e9c4\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5e02f7cebdf86fa057f0d293b084e9c4\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5e02f7cebdf86fa057f0d293b084e9c4\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1b5fc5e1d71643e2466bb818b263e88e\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8211487442cde077b9cc5426bf535a4c\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ebc57f3b4b2b79021e1544eda03da5c8\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\efcbe048319b93d5c7849829a8efd617\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\efcbe048319b93d5c7849829a8efd617\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\efcbe048319b93d5c7849829a8efd617\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1214f860990f79df9d3230cca6181343\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\45f210910fc802bfbe8fbafeb5339aaf\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\0dc3d954c778ef3a31c4a57c0c97f1b9\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\bc20d8662f4cf9ea4fe04656c5b5e139\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\0dc3d954c778ef3a31c4a57c0c97f1b9\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\96906c0ba304237d997b55d99ef6a40d\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\029020f54a1ff641dc525e848a21a322\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\a286073b18693474ea0cd321b016e070\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\029020f54a1ff641dc525e848a21a322\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\a286073b18693474ea0cd321b016e070\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnsvg_EXPORTS -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\96906c0ba304237d997b55d99ef6a40d\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\RNEdgeToEdge-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6f4y6r24/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\261~1.109\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=armv7-none-linux-androideabi24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp.o -c \"C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp\"", "file": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp"}]