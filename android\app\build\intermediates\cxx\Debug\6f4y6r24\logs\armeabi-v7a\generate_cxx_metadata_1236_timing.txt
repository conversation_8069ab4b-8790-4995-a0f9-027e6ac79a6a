# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 17ms
  generate-prefab-packages
    [gap of 23ms]
    exec-prefab 1375ms
    [gap of 19ms]
  generate-prefab-packages completed in 1417ms
  execute-generate-process
    exec-configure 1138ms
    [gap of 106ms]
  execute-generate-process completed in 1245ms
  [gap of 40ms]
generate_cxx_metadata completed in 2755ms

