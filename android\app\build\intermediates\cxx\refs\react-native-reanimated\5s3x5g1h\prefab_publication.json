{"installationFolder": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6s3w4p4v\\obj\\arm64-v8a\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6s3w4p4v\\arm64-v8a\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6s3w4p4v\\obj\\arm64-v8a\\libworklets.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6s3w4p4v\\arm64-v8a\\android_gradle_build.json"}]}]}}