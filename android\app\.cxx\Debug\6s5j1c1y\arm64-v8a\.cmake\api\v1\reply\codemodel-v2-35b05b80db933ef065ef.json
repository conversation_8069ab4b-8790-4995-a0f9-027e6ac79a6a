{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-4683f809569b67f89d2e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-8ac66d0c083cf8955f5c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-2351d4c248d762de9010.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-9a580921581774c6da03.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-00e9b919894abd22435c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-af2014b6828ca68111ec.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [7]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-2852d2d3e77f67dbcce0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-50cf981cb79e96e37667.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-32b08093aa374094db23.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-3e6a021624e4ad75be5b.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-e241ea17330f97b8f6c3.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-8290edf9a1b060209d20.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-ebd45ae58aa1df70aa65.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-bcab8f47fa8fb97aa641.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-33a0eeeb271776179761.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-4f87404ee4801151ce65.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-dcfa8973b4ddfb587d58.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/TMS-App-main/android/app/.cxx/Debug/6s5j1c1y/arm64-v8a", "source": "C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}