{"name": "tms_app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:android:preview": "eas build --platform android --profile preview", "build:android:production": "eas build --platform android --profile production", "build:ios": "eas build --platform ios", "build:ios:preview": "eas build --platform ios --profile preview", "build:ios:production": "eas build --platform ios --profile production", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "lint": "eslint .", "test": "jest"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0", "events": "^3.3.0", "expo": "53.0.18", "expo-constants": "~17.1.6", "expo-file-system": "~18.1.11", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-print": "^14.1.4", "expo-router": "~5.1.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.16", "glob": "^11.0.3", "minimatch": "^10.0.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-drawer-layout": "^4.1.8", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-polyfill-globals": "^3.1.0", "react-native-quick-crypto": "^0.7.14", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "readable-stream": "^4.7.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-define-polyfill-provider": "^0.6.4", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-expo": "~13.0.0", "react-native-dotenv": "^3.4.11", "typescript": "~5.8.3"}, "private": true}