ninja: Entering directory `C:\Users\<USER>\Downloads\TMS-App-main\android\app\.cxx\Debug\6f4y6r24\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[2/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[3/12] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[4/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[5/12] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o
[6/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[7/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[8/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[9/12] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[10/12] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[11/12] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Downloads/TMS-App-main/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[12/12] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\arm64-v8a\libappmodules.so"
