-- Add medication columns to pre_cert_med_list_form table
ALTER TABLE pre_cert_med_list_form
-- <PERSON><PERSON> medications
ADD COLUMN IF NOT EXISTS sertraline JSONB,
ADD COLUMN IF NOT EXISTS fluoxetine JSONB,
ADD COLUMN IF NOT EXISTS citalopram JSONB,
ADD COLUMN IF NOT EXISTS fluvoxamine JSONB,
ADD COLUMN IF NOT EXISTS paroxetine JSONB,
ADD COLUMN IF NOT EXISTS paroxetine_cr JSONB,
ADD COLUMN IF NOT EXISTS escitalopram JSONB,
ADD COLUMN IF NOT EXISTS vilazodone JSONB,
ADD COLUMN IF NOT EXISTS vortioxetine JSONB,

-- SNRI medications
ADD COLUMN IF NOT EXISTS venlafaxine JSONB,
ADD COLUMN IF NOT EXISTS duloxetine JSONB,
ADD COLUMN IF NOT EXISTS desvenlafaxine JSONB,
ADD COLUMN IF NOT EXISTS levomilnacipran JSONB,
ADD COLUMN IF NOT EXISTS milnacipran JSONB,

-- TRICYCLIC medications
ADD COLUMN IF NOT EXISTS amitriptyline JSONB,
ADD COLUMN IF NOT EXISTS imipramine JSONB,
ADD COLUMN IF NOT EXISTS desipramine JSONB,
ADD COLUMN IF NOT EXISTS trimipramine JSONB,
ADD COLUMN IF NOT EXISTS clomipramine JSONB,
ADD COLUMN IF NOT EXISTS maprotiline JSONB,
ADD COLUMN IF NOT EXISTS doxepin JSONB,
ADD COLUMN IF NOT EXISTS nomifensine JSONB,
ADD COLUMN IF NOT EXISTS nortriptyline JSONB,
ADD COLUMN IF NOT EXISTS protriptyline JSONB,
ADD COLUMN IF NOT EXISTS amoxapine JSONB,

-- MAOI medications
ADD COLUMN IF NOT EXISTS phenelzine JSONB,
ADD COLUMN IF NOT EXISTS selegiline JSONB,
ADD COLUMN IF NOT EXISTS selegiline_patch JSONB,
ADD COLUMN IF NOT EXISTS isocarboxazid JSONB,
ADD COLUMN IF NOT EXISTS tranylcypromine JSONB,

-- ATYPICAL medications
ADD COLUMN IF NOT EXISTS bupropion JSONB,
ADD COLUMN IF NOT EXISTS nefazodone JSONB,
ADD COLUMN IF NOT EXISTS trazodone JSONB,
ADD COLUMN IF NOT EXISTS mirtazapine JSONB,

-- AUGMENTING AGENT medications
ADD COLUMN IF NOT EXISTS aripiprazole JSONB,
ADD COLUMN IF NOT EXISTS ziprasidone JSONB,
ADD COLUMN IF NOT EXISTS risperidone JSONB,
ADD COLUMN IF NOT EXISTS quetiapine JSONB,
ADD COLUMN IF NOT EXISTS olanzapine JSONB,
ADD COLUMN IF NOT EXISTS asenapine JSONB,
ADD COLUMN IF NOT EXISTS cariprazine JSONB,
ADD COLUMN IF NOT EXISTS lurasidone JSONB,
ADD COLUMN IF NOT EXISTS clozapine JSONB,
ADD COLUMN IF NOT EXISTS paliperidone JSONB,
ADD COLUMN IF NOT EXISTS brexpiprazole JSONB,
ADD COLUMN IF NOT EXISTS lithium JSONB,
ADD COLUMN IF NOT EXISTS gabapentin JSONB,
ADD COLUMN IF NOT EXISTS lamotrigine JSONB,
ADD COLUMN IF NOT EXISTS topiramate JSONB; 