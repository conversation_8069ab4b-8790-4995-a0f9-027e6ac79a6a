{"expo": {"jsEngine": "hermes", "scheme": "tmsapp", "name": "TmsApp", "slug": "tms_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "description": "TMS of Emerald Coast - Professional mental health services and TMS therapy. Book consultations, complete assessments, and access comprehensive mental health care.", "assetBundlePatterns": ["assets/**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.emrald.tms", "buildNumber": "2", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIStatusBarStyle": "UIStatusBarStyleLightContent", "UIViewControllerBasedStatusBarAppearance": false}}, "android": {"jsEngine": "hermes", "versionCode": 1, "ndkVersion": "26.1.10909125", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#2c5264"}, "edgeToEdgeEnabled": true, "package": "com.emrald.tms", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#2c5264", "imageWidth": 150, "borderRadius": 20}]], "extra": {"router": {}, "eas": {"projectId": "aec7ddf4-9474-4131-9a9a-4b9309bf4176"}}, "runtimeVersion": "1.0.0"}}