{"expo": {"jsEngine": "hermes", "scheme": "tmsapp", "name": "TmsApp", "slug": "tms_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "description": "TMS of Emerald Coast - Professional mental health services and TMS therapy. Book consultations, complete assessments, and access comprehensive mental health care.", "assetBundlePatterns": ["assets/**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.emrald.tms", "buildNumber": "2", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIStatusBarStyle": "UIStatusBarStyleLightContent", "UIViewControllerBasedStatusBarAppearance": false}}, "android": {"jsEngine": "hermes", "versionCode": 1, "ndkVersion": "26.1.10909125", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#2c5264"}, "edgeToEdgeEnabled": true, "package": "com.emrald.tms", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#2c5264", "imageWidth": 150, "borderRadius": 20}]], "extra": {"router": {}, "eas": {"projectId": "aec7ddf4-9474-4131-9a9a-4b9309bf4176"}, "REACT_APP_SUPABASE_URL": "https://abpavtpsxwsgelbpnlvp.supabase.co", "REACT_APP_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFicGF2dHBzeHdzZ2VsYnBubHZwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTcwNDYzMDksImV4cCI6MjA3MjYyMjMwOX0.-0WNNGViYLye8MCqHNwljn1TqySUM4gJ8viyM4xc4wI", "RESEND_API_KEY": "re_MvZGF9wY_9RVDB46EHfNM3cFWMpgar2Uv"}, "runtimeVersion": "1.0.0"}}