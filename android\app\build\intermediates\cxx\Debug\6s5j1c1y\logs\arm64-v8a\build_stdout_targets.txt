ninja: Entering directory `C:\Users\<USER>\Downloads\TMS-App-main\android\app\.cxx\Debug\6s5j1c1y\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[4/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[5/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o
[6/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[7/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[8/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[9/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o
[10/76] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[11/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o
[12/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o
[13/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o
[14/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o
[15/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[16/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o
[17/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o
[18/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[19/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[20/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[21/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[22/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/EventEmitters.cpp.o
[23/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[24/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewState.cpp.o
[25/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o
[26/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o
[27/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/ShadowNodes.cpp.o
[28/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o
[29/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o
[30/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[31/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/States.cpp.o
[32/76] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6s5j1c1y\obj\arm64-v8a\libreact_codegen_safeareacontext.so"
FAILED: C:/Users/<USER>/Downloads/TMS-App-main/android/app/build/intermediates/cxx/Debug/6s5j1c1y/obj/arm64-v8a/libreact_codegen_safeareacontext.so 
cmd.exe /C "cd . && C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\271~1.122\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot="C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -fPIC -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -shared -Wl,-soname,libreact_codegen_safeareacontext.so -o "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6s5j1c1y\obj\arm64-v8a\libreact_codegen_safeareacontext.so" safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o  "C:/Users/<USER>/.gradle/caches/8.13/transforms/2902bba01e106f6c522482d1a2399eaf/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm && cd ."
ld.lld: error: undefined symbol: std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::~basic_string()
>>> referenced by safeareacontext-generated.cpp:22 (C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp:22)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(facebook::react::NativeSafeAreaContextSpecJSI::NativeSafeAreaContextSpecJSI(facebook::react::JavaTurboModule::InitParams const&))
>>> referenced by safeareacontext-generated.cpp:22 (C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp:22)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(facebook::react::NativeSafeAreaContextSpecJSI::NativeSafeAreaContextSpecJSI(facebook::react::JavaTurboModule::InitParams const&))
>>> referenced by safeareacontext-generated.cpp:17 (C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp:17)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(facebook::react::__hostFunction_NativeSafeAreaContextSpecJSI_getConstants(facebook::jsi::Runtime&, facebook::react::TurboModule&, facebook::jsi::Value const*, unsigned long))
>>> referenced 68 more times

ld.lld: error: undefined symbol: operator delete(void*)
>>> referenced by safeareacontext.h:22 (C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext.h:22)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(facebook::react::NativeSafeAreaContextSpecJSI::~NativeSafeAreaContextSpecJSI())
>>> referenced by shared_ptr.h:246 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:246)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>::~__shared_ptr_emplace())
>>> referenced by TurboModule.h:49 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/ReactCommon/TurboModule.h:49)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(facebook::react::TurboModule::~TurboModule())
>>> referenced 35 more times

ld.lld: error: undefined symbol: std::__ndk1::__shared_weak_count::~__shared_weak_count()
>>> referenced by shared_ptr.h:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:263)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>::__shared_ptr_emplace[abi:ne180000]<facebook::react::JavaTurboModule::InitParams const&, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>, 0>(std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>, facebook::react::JavaTurboModule::InitParams const&))
>>> referenced by shared_ptr.h:246 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:246)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>::~__shared_ptr_emplace())
>>> referenced by shared_ptr.h:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:263)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(std::__ndk1::__shared_ptr_emplace<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>, std::__ndk1::allocator<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>>>::__shared_ptr_emplace[abi:ne180000]<facebook::react::ShadowNodeFragment const&, std::__ndk1::shared_ptr<facebook::react::ShadowNodeFamily const> const&, facebook::react::ShadowNodeTraits, std::__ndk1::allocator<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>>, 0>(std::__ndk1::allocator<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>>, facebook::react::ShadowNodeFragment const&, std::__ndk1::shared_ptr<facebook::react::ShadowNodeFamily const> const&, facebook::react::ShadowNodeTraits&&))
>>> referenced 21 more times

ld.lld: error: undefined symbol: vtable for __cxxabiv1::__si_class_type_info
>>> referenced by safeareacontext-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(typeinfo for facebook::react::NativeSafeAreaContextSpecJSI)
>>> referenced by safeareacontext-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(typeinfo for std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>)
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(typeinfo for facebook::react::TurboModule)
>>> referenced 26 more times
>>> the vtable symbol may be undefined because the class is missing its key function (see https://lld.llvm.org/missingkeyfunction)

ld.lld: error: undefined symbol: std::__ndk1::__shared_weak_count::__get_deleter(std::type_info const&) const
>>> referenced by safeareacontext-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(vtable for std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>)
>>> referenced by ComponentDescriptors.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(vtable for std::__ndk1::__shared_ptr_emplace<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>, std::__ndk1::allocator<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>>>)
>>> referenced by ComponentDescriptors.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(vtable for std::__ndk1::__shared_ptr_emplace<facebook::react::RNCSafeAreaProviderProps const, std::__ndk1::allocator<facebook::react::RNCSafeAreaProviderProps const>>)
>>> referenced 8 more times

ld.lld: error: undefined symbol: typeinfo for std::__ndk1::__shared_weak_count
>>> referenced by safeareacontext-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c7dab0d178ee3bd66f90dc036bf5f8fe/codegen/jni/safeareacontext-generated.cpp.o:(typeinfo for std::__ndk1::__shared_ptr_emplace<facebook::react::NativeSafeAreaContextSpecJSI, std::__ndk1::allocator<facebook::react::NativeSafeAreaContextSpecJSI>>)
>>> referenced by ComponentDescriptors.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(typeinfo for std::__ndk1::__shared_ptr_emplace<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>, std::__ndk1::allocator<facebook::react::ConcreteViewShadowNode<&facebook::react::RNCSafeAreaProviderComponentName.<char const at offset 0>, facebook::react::RNCSafeAreaProviderProps, facebook::react::RNCSafeAreaProviderEventEmitter, facebook::react::RNCSafeAreaProviderState, false>>>)
>>> referenced by ComponentDescriptors.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(typeinfo for std::__ndk1::__shared_ptr_emplace<facebook::react::RNCSafeAreaProviderProps const, std::__ndk1::allocator<facebook::react::RNCSafeAreaProviderProps const>>)
>>> referenced 8 more times

ld.lld: error: undefined symbol: std::__ndk1::__next_prime(unsigned long)
>>> referenced by __hash_table:1657 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__hash_table:1657)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(void std::__ndk1::__hash_table<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::__unordered_map_hasher<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::__unordered_map_equal<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::allocator<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>>>::__rehash<true>(unsigned long))
>>> referenced by __hash_table:1665 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__hash_table:1665)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(void std::__ndk1::__hash_table<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::__unordered_map_hasher<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::__unordered_map_equal<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::allocator<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::TurboModule::MethodMetadata>>>::__rehash<true>(unsigned long))
>>> referenced by __hash_table:1657 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__hash_table:1657)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(void std::__ndk1::__hash_table<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::RawValue>, std::__ndk1::__unordered_map_hasher<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::RawValue>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::__unordered_map_equal<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::RawValue>, std::__ndk1::equal_to<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, std::__ndk1::hash<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>>, true>, std::__ndk1::allocator<std::__ndk1::__hash_value_type<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>, facebook::react::RawValue>>>::__rehash<true>(unsigned long))
>>> referenced 1 more times

ld.lld: error: undefined symbol: __cxa_pure_virtual
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(vtable for facebook::react::NativeSafeAreaContextCxxSpecJSI)
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(vtable for std::__ndk1::__function::__base<facebook::jsi::Value (facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const*, unsigned long)>)
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(vtable for std::__ndk1::__function::__base<facebook::jsi::Value (facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const*, unsigned long)>)
>>> referenced 40 more times

ld.lld: error: undefined symbol: vtable for __cxxabiv1::__class_type_info
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(typeinfo for std::__ndk1::__function::__base<facebook::jsi::Value (facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const*, unsigned long)>)
>>> referenced by safeareacontextJSI-generated.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ae6edf3a95bddbbd82becc708e383d5/safeareacontextJSI-generated.cpp.o:(typeinfo for facebook::react::TurboModule::create(facebook::jsi::Runtime&, facebook::jsi::PropNameID const&)::'lambda'(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const*, unsigned long))
>>> referenced by EventEmitters.cpp
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/EventEmitters.cpp.o:(typeinfo for std::__ndk1::__function::__base<facebook::jsi::Value (facebook::jsi::Runtime&)>)
>>> referenced 8 more times
>>> the vtable symbol may be undefined because the class is missing its key function (see https://lld.llvm.org/missingkeyfunction)

ld.lld: error: undefined symbol: __cxa_begin_catch
>>> referenced by propsConversions.h:132 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/propsConversions.h:132)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RNCSafeAreaViewMode facebook::react::convertRawProp<facebook::react::RNCSafeAreaViewMode, facebook::react::RNCSafeAreaViewMode>(facebook::react::PropsParserContext const&, facebook::react::RawProps const&, char const*, facebook::react::RNCSafeAreaViewMode const&, facebook::react::RNCSafeAreaViewMode const&, char const*, char const*))
>>> referenced by propsConversions.h:132 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/propsConversions.h:132)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RNCSafeAreaViewEdgesStruct facebook::react::convertRawProp<facebook::react::RNCSafeAreaViewEdgesStruct, facebook::react::RNCSafeAreaViewEdgesStruct>(facebook::react::PropsParserContext const&, facebook::react::RawProps const&, char const*, facebook::react::RNCSafeAreaViewEdgesStruct const&, facebook::react::RNCSafeAreaViewEdgesStruct const&, char const*, char const*))
>>> referenced by ostream:683 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:683)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::__put_character_sequence[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&, char const*, unsigned long))
>>> referenced 1 more times

ld.lld: error: undefined symbol: __cxa_end_catch
>>> referenced by propsConversions.h:139 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/propsConversions.h:139)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RNCSafeAreaViewMode facebook::react::convertRawProp<facebook::react::RNCSafeAreaViewMode, facebook::react::RNCSafeAreaViewMode>(facebook::react::PropsParserContext const&, facebook::react::RawProps const&, char const*, facebook::react::RNCSafeAreaViewMode const&, facebook::react::RNCSafeAreaViewMode const&, char const*, char const*))
>>> referenced by propsConversions.h:139 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/propsConversions.h:139)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RNCSafeAreaViewMode facebook::react::convertRawProp<facebook::react::RNCSafeAreaViewMode, facebook::react::RNCSafeAreaViewMode>(facebook::react::PropsParserContext const&, facebook::react::RawProps const&, char const*, facebook::react::RNCSafeAreaViewMode const&, facebook::react::RNCSafeAreaViewMode const&, char const*, char const*))
>>> referenced by propsConversions.h:139 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/propsConversions.h:139)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RNCSafeAreaViewEdgesStruct facebook::react::convertRawProp<facebook::react::RNCSafeAreaViewEdgesStruct, facebook::react::RNCSafeAreaViewEdgesStruct>(facebook::react::PropsParserContext const&, facebook::react::RawProps const&, char const*, facebook::react::RNCSafeAreaViewEdgesStruct const&, facebook::react::RNCSafeAreaViewEdgesStruct const&, char const*, char const*))
>>> referenced 3 more times

ld.lld: error: undefined symbol: __cxa_allocate_exception
>>> referenced by variant:287 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:287)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::__throw_bad_variant_access[abi:ne180000]())
>>> referenced by Exception.h:50 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/folly/lang/Exception.h:50)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(void folly::throw_exception<folly::TypeError>(folly::TypeError&&))
>>> referenced by function.h:78 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/function.h:78)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(std::__ndk1::__throw_bad_function_call[abi:ne180000]())
>>> referenced 3 more times

ld.lld: error: undefined symbol: typeinfo for std::bad_variant_access
>>> referenced by variant:287 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:287)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::__throw_bad_variant_access[abi:ne180000]())
>>> referenced by variant:287 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:287)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::__throw_bad_variant_access[abi:ne180000]())

ld.lld: error: undefined symbol: __cxa_throw
>>> referenced by variant:287 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:287)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::__throw_bad_variant_access[abi:ne180000]())
>>> referenced by Exception.h:50 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/folly/lang/Exception.h:50)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(void folly::throw_exception<folly::TypeError>(folly::TypeError&&))
>>> referenced by function.h:78 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/function.h:78)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(std::__ndk1::__throw_bad_function_call[abi:ne180000]())
>>> referenced 3 more times

ld.lld: error: undefined symbol: vtable for std::bad_variant_access
>>> referenced by variant:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:263)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::bad_variant_access::bad_variant_access())
>>> referenced by variant:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:263)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::bad_variant_access::bad_variant_access())
>>> the vtable symbol may be undefined because the class is missing its key function (see https://lld.llvm.org/missingkeyfunction)

ld.lld: error: undefined symbol: std::exception::~exception()
>>> referenced by variant:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/variant:263)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::bad_variant_access::~bad_variant_access())
>>> referenced by function.h:67 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/function.h:67)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o:(std::__ndk1::bad_function_call::~bad_function_call())

ld.lld: error: undefined symbol: std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_string(std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&)
>>> referenced by RawValue.h:439 (C:/Users/<USER>/.gradle/caches/8.13/transforms/e5e613c135358825ca859c6b38975850/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/renderer/core/RawValue.h:439)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(facebook::react::RawValue::castValue(folly::dynamic const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>*))
>>> referenced by tuple:1409 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/tuple:1409)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::pair<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const, facebook::react::RawValue>::pair[abi:ne180000]<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, 0ul>(std::__ndk1::piecewise_construct_t, std::__ndk1::tuple<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&>&, std::__ndk1::tuple<>&, std::__ndk1::__tuple_indices<0ul>, std::__ndk1::__tuple_indices<...>))
>>> referenced by pair.h:207 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__utility/pair.h:207)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::pair<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const, facebook::react::RawValue>::pair[abi:ne180000]<std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>&, facebook::react::RawValue, 0>(std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>&, facebook::react::RawValue&&))
>>> referenced 8 more times

ld.lld: error: undefined symbol: std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>::sentry::sentry(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&)
>>> referenced by ostream:669 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:669)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::__put_character_sequence[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&, char const*, unsigned long))

ld.lld: error: undefined symbol: std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>::sentry::~sentry()
>>> referenced by ostream:683 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:683)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::__put_character_sequence[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&, char const*, unsigned long))
>>> referenced by ostream:683 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:683)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::__put_character_sequence[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&, char const*, unsigned long))

ld.lld: error: undefined symbol: std::__ndk1::ios_base::__set_badbit_and_consider_rethrow()
>>> referenced by ostream:684 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:684)
>>>               safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::__put_character_sequence[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&, char const*, unsigned long))

ld.lld: error: too many errors emitted, stopping now (use --error-limit=0 to see all errors)
CLANG_~1: error: linker command failed with exit code 1 (use -v to see invocation)
[33/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/145dc39712be167fc121da32c01dd086/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[34/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5e02f7cebdf86fa057f0d293b084e9c4/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[35/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[36/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1b5fc5e1d71643e2466bb818b263e88e/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[37/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[38/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[39/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[40/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5e02f7cebdf86fa057f0d293b084e9c4/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[41/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
ninja: build stopped: subcommand failed.
