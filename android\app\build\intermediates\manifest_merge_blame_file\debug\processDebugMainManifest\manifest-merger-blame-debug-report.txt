1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.emrald.tms"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:5:3-75
11-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:2:3-76
12-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:2:20-74
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:3:3-64
13-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission
14-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:4:3-77
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:4:20-75
16        android:maxSdkVersion="32" />
16-->[BareExpo:expo.modules.image:2.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d32897edc933358dafb222bffdc18a2\transformed\expo.modules.image-2.3.2\AndroidManifest.xml:17:9-35
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:6:3-63
17-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:6:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:7:3-78
18-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:7:20-76
19
20    <queries>
20-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:8:3-14:13
21        <intent>
21-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:9:5-13:14
22            <action android:name="android.intent.action.VIEW" />
22-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:10:7-58
22-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:10:15-56
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:11:7-67
24-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:11:17-65
25
26            <data android:scheme="https" />
26-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:12:7-37
26-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:12:13-35
27        </intent>
28        <!-- Needs to be explicitly declared on Android R+ -->
29        <package android:name="com.google.android.apps.maps" /> <!-- Query open documents -->
29-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
29-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
30        <intent>
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
31            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
32        </intent>
33        <intent>
33-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
34
35            <!-- Required for file sharing if targeting API 30 -->
36            <action android:name="android.intent.action.SEND" />
36-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
36-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
37
38            <data android:mimeType="*/*" />
38-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:12:7-37
39        </intent>
40    </queries>
41
42    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
42-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
42-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
43
44    <uses-feature
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
45        android:glEsVersion="0x00020000"
45-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
46        android:required="true" />
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
47
48    <uses-permission android:name="android.permission.WAKE_LOCK" />
48-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
48-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
49    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
49-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
49-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
50-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
51
52    <permission
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
53        android:name="com.emrald.tms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.emrald.tms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
57
58    <application
58-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:3-32:17
59        android:name="com.emrald.tms.MainApplication"
59-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:16-47
60        android:allowBackup="true"
60-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:162-188
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:icon="@mipmap/ic_launcher"
64-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:81-115
65        android:label="@string/app_name"
65-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:48-80
66        android:roundIcon="@mipmap/ic_launcher_round"
66-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:116-161
67        android:supportsRtl="true"
67-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:221-247
68        android:theme="@style/AppTheme"
68-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:15:189-220
69        android:usesCleartextTraffic="true" >
69-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\debug\AndroidManifest.xml:6:18-53
70        <meta-data
70-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:16:5-83
71            android:name="expo.modules.updates.ENABLED"
71-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:16:16-59
72            android:value="false" />
72-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:16:60-81
73        <meta-data
73-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:17:5-119
74            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
74-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:17:16-72
75            android:value="@string/expo_runtime_version" />
75-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:17:73-117
76        <meta-data
76-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:18:5-105
77            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
77-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:18:16-80
78            android:value="ALWAYS" />
78-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:18:81-103
79        <meta-data
79-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:19:5-99
80            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
80-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:19:16-79
81            android:value="0" />
81-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:19:80-97
82
83        <activity
83-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:5-31:16
84            android:name="com.emrald.tms.MainActivity"
84-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:15-43
85            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
85-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:44-134
86            android:exported="true"
86-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:256-279
87            android:launchMode="singleTask"
87-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:135-166
88            android:screenOrientation="portrait"
88-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:280-316
89            android:theme="@style/Theme.App.SplashScreen"
89-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:210-255
90            android:windowSoftInputMode="adjustResize" >
90-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:20:167-209
91            <intent-filter>
91-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:21:7-24:23
92                <action android:name="android.intent.action.MAIN" />
92-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:22:9-60
92-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:22:17-58
93
94                <category android:name="android.intent.category.LAUNCHER" />
94-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:23:9-68
94-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:23:19-66
95            </intent-filter>
96            <intent-filter>
96-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:25:7-30:23
97                <action android:name="android.intent.action.VIEW" />
97-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:10:7-58
97-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:10:15-56
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:27:9-67
99-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:27:19-65
100                <category android:name="android.intent.category.BROWSABLE" />
100-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:11:7-67
100-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:11:17-65
101
102                <data android:scheme="tmsapp" />
102-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:12:7-37
102-->C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\AndroidManifest.xml:12:13-35
103            </intent-filter>
104        </activity>
105
106        <provider
106-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
107            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
107-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
108            android:authorities="com.emrald.tms.fileprovider"
108-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
109            android:exported="false"
109-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
110            android:grantUriPermissions="true" >
110-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
111            <meta-data
111-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
113                android:resource="@xml/file_provider_paths" />
113-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
114        </provider>
115
116        <meta-data
116-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
117            android:name="org.unimodules.core.AppLoader#react-native-headless"
117-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
118            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
118-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
119        <meta-data
119-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
120            android:name="com.facebook.soloader.enabled"
120-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
121            android:value="true" />
121-->[:expo-modules-core] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
122
123        <activity
123-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5e613c135358825ca859c6b38975850\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
124            android:name="com.facebook.react.devsupport.DevSettingsActivity"
124-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5e613c135358825ca859c6b38975850\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
125            android:exported="false" />
125-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5e613c135358825ca859c6b38975850\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
126
127        <meta-data
127-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0033a94446b3d18e08ac984d148f43\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
128            android:name="com.google.android.gms.version"
128-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0033a94446b3d18e08ac984d148f43\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
129            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
129-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0033a94446b3d18e08ac984d148f43\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
130        <uses-library
130-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
131            android:name="org.apache.http.legacy"
131-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
132            android:required="false" />
132-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
133
134        <activity
134-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d01614c8721acfa4a13c278e25c2fb9e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
135            android:name="com.google.android.gms.common.api.GoogleApiActivity"
135-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d01614c8721acfa4a13c278e25c2fb9e\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
136            android:exported="false"
136-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d01614c8721acfa4a13c278e25c2fb9e\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
137            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
137-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d01614c8721acfa4a13c278e25c2fb9e\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
138
139        <provider
139-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
140            android:name="expo.modules.filesystem.FileSystemFileProvider"
140-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
141            android:authorities="com.emrald.tms.FileSystemFileProvider"
141-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
142            android:exported="false"
142-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
143            android:grantUriPermissions="true" >
143-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
144            <meta-data
144-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
145                android:name="android.support.FILE_PROVIDER_PATHS"
145-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
146                android:resource="@xml/file_system_provider_paths" />
146-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
147        </provider>
148        <provider
148-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
149            android:name="expo.modules.sharing.SharingFileProvider"
149-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
150            android:authorities="com.emrald.tms.SharingFileProvider"
150-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
151            android:exported="false"
151-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
152            android:grantUriPermissions="true" >
152-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
153            <meta-data
153-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
154                android:name="android.support.FILE_PROVIDER_PATHS"
154-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
155                android:resource="@xml/sharing_provider_paths" />
155-->[:react-native-webview] C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
156        </provider>
157
158        <meta-data
158-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bec34fcb1747a67b78a66ea45d73cfc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
159            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
159-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bec34fcb1747a67b78a66ea45d73cfc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
160            android:value="GlideModule" />
160-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bec34fcb1747a67b78a66ea45d73cfc\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
161
162        <provider
162-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
164            android:authorities="com.emrald.tms.androidx-startup"
164-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
166            <meta-data
166-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
167                android:name="androidx.work.WorkManagerInitializer"
167-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
168                android:value="androidx.startup" />
168-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
169            <meta-data
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc13aa19facc7d4ad4e03f7fba8cdbe0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.emoji2.text.EmojiCompatInitializer"
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc13aa19facc7d4ad4e03f7fba8cdbe0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
171                android:value="androidx.startup" />
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc13aa19facc7d4ad4e03f7fba8cdbe0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59bc4cfaf0d62ef7ff8e8f4ac580a33\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
173-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59bc4cfaf0d62ef7ff8e8f4ac580a33\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
174                android:value="androidx.startup" />
174-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59bc4cfaf0d62ef7ff8e8f4ac580a33\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
175            <meta-data
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
177                android:value="androidx.startup" />
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
178        </provider>
179
180        <service
180-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
181            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
181-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
183            android:enabled="@bool/enable_system_alarm_service_default"
183-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
184            android:exported="false" />
184-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
185        <service
185-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
186            android:name="androidx.work.impl.background.systemjob.SystemJobService"
186-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
188            android:enabled="@bool/enable_system_job_service_default"
188-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
189            android:exported="true"
189-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
190            android:permission="android.permission.BIND_JOB_SERVICE" />
190-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
191        <service
191-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
192            android:name="androidx.work.impl.foreground.SystemForegroundService"
192-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
194            android:enabled="@bool/enable_system_foreground_service_default"
194-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
195            android:exported="false" />
195-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
196
197        <receiver
197-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
198            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
198-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
200            android:enabled="true"
200-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
201            android:exported="false" />
201-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
202        <receiver
202-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
203            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
203-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
208                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
208-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
208-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
209                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
209-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
209-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
213-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
215            android:enabled="false"
215-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
218                <action android:name="android.intent.action.BATTERY_OKAY" />
218-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
218-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
219                <action android:name="android.intent.action.BATTERY_LOW" />
219-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
219-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
223            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
223-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
225            android:enabled="false"
225-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
226            android:exported="false" >
226-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
227            <intent-filter>
227-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
228                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
228-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
228-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
229                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
229-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
229-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
230            </intent-filter>
231        </receiver>
232        <receiver
232-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
233            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
233-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
234            android:directBootAware="false"
234-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
235            android:enabled="false"
235-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
236            android:exported="false" >
236-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
237            <intent-filter>
237-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
238                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
238-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
238-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
239            </intent-filter>
240        </receiver>
241        <receiver
241-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
242            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
242-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
244            android:enabled="false"
244-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
245            android:exported="false" >
245-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
246            <intent-filter>
246-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
247                <action android:name="android.intent.action.BOOT_COMPLETED" />
247-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
247-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
248                <action android:name="android.intent.action.TIME_SET" />
248-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
248-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
249                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
249-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
249-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
250            </intent-filter>
251        </receiver>
252        <receiver
252-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
253            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
253-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
255            android:enabled="@bool/enable_system_alarm_service_default"
255-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
256            android:exported="false" >
256-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
257            <intent-filter>
257-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
258                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
258-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
258-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
259            </intent-filter>
260        </receiver>
261        <receiver
261-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
262            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
262-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
263            android:directBootAware="false"
263-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
264            android:enabled="true"
264-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
265            android:exported="true"
265-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
266            android:permission="android.permission.DUMP" >
266-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
267            <intent-filter>
267-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
268                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
268-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
268-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
269            </intent-filter>
270        </receiver>
271
272        <service
272-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa1b3426e5b28e1b2bd38c87cb12a60\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
273            android:name="androidx.room.MultiInstanceInvalidationService"
273-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa1b3426e5b28e1b2bd38c87cb12a60\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
274            android:directBootAware="true"
274-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa1b3426e5b28e1b2bd38c87cb12a60\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
275            android:exported="false" />
275-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa1b3426e5b28e1b2bd38c87cb12a60\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
276
277        <receiver
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
278            android:name="androidx.profileinstaller.ProfileInstallReceiver"
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
279            android:directBootAware="false"
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
280            android:enabled="true"
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
281            android:exported="true"
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
282            android:permission="android.permission.DUMP" >
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
284                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
285            </intent-filter>
286            <intent-filter>
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
287                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
288            </intent-filter>
289            <intent-filter>
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
290                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
291            </intent-filter>
292            <intent-filter>
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
293                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
294            </intent-filter>
295        </receiver>
296    </application>
297
298</manifest>
