ninja: Entering directory `C:\Users\<USER>\Downloads\TMS-App-main\android\app\.cxx\Debug\6f4y6r24\x86_64'
[0/2] Re-checking globbed directories...
[1/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[2/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[3/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[4/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[5/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[6/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[7/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o
[8/76] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[9/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp.o
[10/76] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[11/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/RNDateTimePickerCGenJSI-generated.cpp.o
[12/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o
[13/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o
[14/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[15/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o
[16/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[17/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[18/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[19/76] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o
[20/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[21/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/EventEmitters.cpp.o
[22/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[23/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewState.cpp.o
[24/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/States.cpp.o
[25/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b553ee758aff9f844e914cac9df00ca3/source/codegen/jni/safeareacontext-generated.cpp.o
[26/76] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[27/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/ComponentDescriptors.cpp.o
[28/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e14c72371b9e72604b62e770a10ddb9f/components/safeareacontext/ShadowNodes.cpp.o
[29/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7a82f62356152691476f484d154cdb43/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[30/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c9a9b9ddd584b804e386d6af7996bf8/safeareacontext/safeareacontextJSI-generated.cpp.o
[31/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5e02f7cebdf86fa057f0d293b084e9c4/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[32/76] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4131df8d2d6f8e891f2d6620be932428/renderer/components/safeareacontext/Props.cpp.o
[33/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56d347500565bec535bf51ef7d49b22/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[34/76] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64\libreact_codegen_safeareacontext.so"
[35/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/145dc39712be167fc121da32c01dd086/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[36/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/efcbe048319b93d5c7849829a8efd617/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[37/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1b5fc5e1d71643e2466bb818b263e88e/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[38/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8211487442cde077b9cc5426bf535a4c/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[39/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[40/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[41/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7f35f0c64f4f506d58cef8000659e3f4/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[42/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56d347500565bec535bf51ef7d49b22/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[43/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[44/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c7b0a5250dae0038c1bb05670df25edb/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[45/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/45f210910fc802bfbe8fbafeb5339aaf/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[46/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0dc3d954c778ef3a31c4a57c0c97f1b9/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[47/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/efcbe048319b93d5c7849829a8efd617/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[48/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5e02f7cebdf86fa057f0d293b084e9c4/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[49/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a286073b18693474ea0cd321b016e070/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
                                           ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                         ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                                          ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "width", $event.source.width);
                                       ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "height", $event.source.height);
                                        ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "uri", $event.source.uri);
                                     ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "source", source);
  ^
C:/Users/<USER>/Downloads/TMS-App-main/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
9 warnings generated.
[50/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0dc3d954c778ef3a31c4a57c0c97f1b9/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[51/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0dc3d954c778ef3a31c4a57c0c97f1b9/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[52/76] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ebc57f3b4b2b79021e1544eda03da5c8/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[53/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a286073b18693474ea0cd321b016e070/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[54/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a286073b18693474ea0cd321b016e070/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[55/76] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64\libreact_codegen_rnscreens.so"
[56/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/029020f54a1ff641dc525e848a21a322/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[57/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[58/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/029020f54a1ff641dc525e848a21a322/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[59/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[60/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[61/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[62/76] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/a286073b18693474ea0cd321b016e070/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[63/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[64/76] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64\libreact_codegen_rnsvg.so"
[65/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[66/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[67/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[68/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[69/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[70/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[71/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[72/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[73/76] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[74/76] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[75/76] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Downloads/TMS-App-main/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[76/76] Linking CXX shared library "C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64\libappmodules.so"
