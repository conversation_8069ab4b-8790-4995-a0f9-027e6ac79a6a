@echo off
"C:\\Program Files\\Java\\jdk-21\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging15430425989144392258\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5e613c135358825ca859c6b38975850\\transformed\\react-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\Downloads\\TMS-App-main\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\5s3x5g1h" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\791c8daba7fff247ac0e74bd0ef2d759\\transformed\\hermes-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2902bba01e106f6c522482d1a2399eaf\\transformed\\fbjni-0.7.0\\prefab"
