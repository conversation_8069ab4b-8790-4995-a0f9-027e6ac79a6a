                        -HC:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\cxx\Debug\6f4y6r24\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\Downloads\TMS-App-main\android\app\.cxx\Debug\6f4y6r24\prefab\x86_64\prefab
-BC:\Users\<USER>\Downloads\TMS-App-main\android\app\.cxx\Debug\6f4y6r24\x86_64
-GNinja
-DPROJECT_BUILD_DIR=C:\Users\<USER>\Downloads\TMS-App-main\android\app\build
-DPROJECT_ROOT_DIR=C:\Users\<USER>\Downloads\TMS-App-main\android
-DREACT_ANDROID_DIR=C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2